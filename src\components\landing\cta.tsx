import Link from "next/link";
import { <PERSON><PERSON> } from "../ui/button";
import { ArrowRight } from "lucide-react";

const CTA = () => {
  return (
    <section className="py-28 px-4 sm:px-6 lg:px-8 bg-indigo-700">
      <div className="max-w-5xl mx-auto text-center">
        <h2 className="text-3xl sm:text-4xl font-bold mb-8 text-white">
          Ready to Transform Your Customer Support? 🚀
        </h2>
        <p className="text-indigo-100 text-xl mb-10 max-w-3xl mx-auto">
          Join thousands of businesses using BetterFAQ AI to provide instant
          answers, reduce support tickets, and increase conversions.
        </p>
        <Link href="/signup">
          <Button
            size="lg"
            className="bg-white text-indigo-700 hover:bg-indigo-50"
          >
            Get Started
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </Link>
      </div>
    </section>
  );
};

export default CTA;
