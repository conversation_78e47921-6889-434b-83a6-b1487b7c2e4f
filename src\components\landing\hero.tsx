import Link from "next/link";
import { <PERSON><PERSON> } from "../ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import { Badge } from "@/components/ui/badge";

const Hero = () => {
  return (
    <section className="pt-40 pb-28 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-indigo-50 to-white">
      <div className="max-w-6xl mx-auto">
        <div className="grid md:grid-cols-2 gap-16 items-center">
          <div>
            <Badge className="mb-6 bg-indigo-100 text-indigo-800 hover:bg-indigo-200 border-none">
              AI-Powered Support ✨
            </Badge>
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold mb-8 leading-tight text-gray-800">
              Double Your Conversions with{" "}
              <span className="bg-gradient-to-r from-indigo-700 to-indigo-500 bg-clip-text text-transparent">
                Instant Answers
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-10 font-medium">
              Our AI-powered FAQ widget answers customer questions instantly,
              builds confidence, and increases purchase rates by 2x. 🚀
            </p>
            <div className="flex flex-col sm:flex-row gap-5">
              <Link href="/signup">
                <Button
                  size="lg"
                  className="bg-indigo-700 hover:bg-indigo-800 text-white font-semibold text-lg"
                >
                  Get Started
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
              <Link href="#demo">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-indigo-600 text-indigo-700 hover:bg-indigo-50 font-semibold text-lg"
                >
                  See Demo
                </Button>
              </Link>
            </div>
          </div>
          <div className="relative">
            <div className="absolute -z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[300px] h-[300px] bg-indigo-300 rounded-full blur-[120px] opacity-30" />
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
              <div className="p-6 border-b border-gray-100 bg-gray-50">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-red-400 mr-2"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-400 mr-2"></div>
                  <div className="w-3 h-3 rounded-full bg-green-400"></div>
                  <div className="ml-4 text-sm text-gray-500">
                    BetterFAQ Widget
                  </div>
                </div>
              </div>
              <div className="p-6">
                <div className="flex flex-col space-y-6">
                  <div className="bg-indigo-100 text-indigo-800 p-4 rounded-lg self-start max-w-[80%]">
                    How does your pricing work? 💰
                  </div>
                  <div className="bg-gray-100 p-4 rounded-lg self-end max-w-[80%]">
                    We offer a free plan with 20 messages per month, and our Pro
                    plan at $9/month includes 1000 messages, analytics, and
                    custom branding.
                  </div>
                  <div className="bg-indigo-100 text-indigo-800 p-4 rounded-lg self-start max-w-[80%]">
                    Can I customize the widget to match my brand?
                  </div>
                  <div className="bg-gray-100 p-4 rounded-lg self-end max-w-[80%]">
                    Yes! Our Pro plan allows full customization of colors,
                    position, and branding to perfectly match your website's
                    design.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
