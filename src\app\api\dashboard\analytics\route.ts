import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { widget, feedback, analytics } from '@/db/schema';
import { eq, and, count, desc, gte } from 'drizzle-orm';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get total stats
    const [totalWidgetsResult] = await db
      .select({ count: count() })
      .from(widget)
      .where(eq(widget.userId, userId));

    const [totalMessages] = await db
      .select({ count: count() })
      .from(analytics)
      .innerJoin(widget, eq(analytics.widgetId, widget.id))
      .where(and(
        eq(widget.userId, userId),
        eq(analytics.eventType, 'message_sent')
      ));

    const [totalFeedbacks] = await db
      .select({ count: count() })
      .from(feedback)
      .innerJoin(widget, eq(feedback.widgetId, widget.id))
      .where(and(
        eq(widget.userId, userId),
        eq(feedback.type, 'feedback')
      ));

    const [totalBugReports] = await db
      .select({ count: count() })
      .from(feedback)
      .innerJoin(widget, eq(feedback.widgetId, widget.id))
      .where(and(
        eq(widget.userId, userId),
        eq(feedback.type, 'bug')
      ));

    const [activeWidgets] = await db
      .select({ count: count() })
      .from(widget)
      .where(and(
        eq(widget.userId, userId),
        eq(widget.isActive, true)
      ));

    // Get recent feedbacks (last 10)
    const recentFeedbacks = await db
      .select({
        id: feedback.id,
        widgetId: feedback.widgetId,
        widgetName: widget.name,
        content: feedback.content,
        createdAt: feedback.createdAt,
      })
      .from(feedback)
      .innerJoin(widget, eq(feedback.widgetId, widget.id))
      .where(and(
        eq(widget.userId, userId),
        eq(feedback.type, 'feedback')
      ))
      .orderBy(desc(feedback.createdAt))
      .limit(10);

    // Get recent bug reports (last 10)
    const recentBugReports = await db
      .select({
        id: feedback.id,
        widgetId: feedback.widgetId,
        widgetName: widget.name,
        title: feedback.content,
        createdAt: feedback.createdAt,
      })
      .from(feedback)
      .innerJoin(widget, eq(feedback.widgetId, widget.id))
      .where(and(
        eq(widget.userId, userId),
        eq(feedback.type, 'bug')
      ))
      .orderBy(desc(feedback.createdAt))
      .limit(10);


    return NextResponse.json({
      stats: {
        totalMessages: totalMessages.count || 0,
        totalFeedbacks: totalFeedbacks.count || 0,
        totalBugReports: totalBugReports.count || 0,
        activeWidgets: activeWidgets.count || 0,
      },
      recentFeedbacks: recentFeedbacks,
      recentBugReports: recentBugReports
    });

  } catch (error) {
    console.error('Dashboard analytics error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
