import Link from "next/link";
import { <PERSON><PERSON> } from "../ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import { Badge } from "@/components/ui/badge";

const Demo = () => {
  return (
    <section id="demo" className="py-28 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-6xl mx-auto">
        <div className="grid md:grid-cols-2 gap-16 items-center">
          <div>
            <Badge className="mb-6 bg-indigo-100 text-indigo-800 hover:bg-indigo-200 border-none">
              Live Demo
            </Badge>
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              See BetterFAQ AI in Action ✨
            </h2>
            <p className="text-gray-600 text-xl mb-8">
              Experience how our AI-powered FAQ widget can transform your
              customer support and increase conversions. Try asking a question!
            </p>
            <ul className="space-y-6 mb-10">
              <li className="flex items-start">
                <span className="text-indigo-500 mr-3 text-xl">✓</span>
                <span className="text-gray-700 text-lg">
                  <strong>Instant Responses:</strong> Get immediate answers to
                  customer questions
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-indigo-500 mr-3 text-xl">✓</span>
                <span className="text-gray-700 text-lg">
                  <strong>Contextual Understanding:</strong> Our AI understands
                  the intent behind questions
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-indigo-500 mr-3 text-xl">✓</span>
                <span className="text-gray-700 text-lg">
                  <strong>24/7 Availability:</strong> Support your customers
                  around the clock
                </span>
              </li>
            </ul>
            <Link href="/signup">
              <Button
                size="lg"
                className="bg-indigo-700 hover:bg-indigo-800 text-white"
              >
                Get Started
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
          <div className="relative">
            <div className="absolute -z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[300px] h-[300px] bg-indigo-300 rounded-full blur-[120px] opacity-30" />
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
              <div className="p-6 border-b border-gray-100 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-red-400 mr-2"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-400 mr-2"></div>
                    <div className="w-3 h-3 rounded-full bg-green-400"></div>
                  </div>
                  <div className="text-sm text-gray-500">Interactive Demo</div>
                </div>
              </div>
              <div className="p-6">
                <div className="flex flex-col space-y-6">
                  <div className="bg-indigo-100 text-indigo-800 p-4 rounded-lg self-start max-w-[80%]">
                    What makes BetterFAQ different from other FAQ solutions? 🤔
                  </div>
                  <div className="bg-gray-100 p-4 rounded-lg self-end max-w-[80%]">
                    Unlike traditional FAQ systems that only show pre-written
                    answers, BetterFAQ uses AI to understand questions in
                    context and provide personalized responses. Our solution
                    learns from interactions to continuously improve, and offers
                    detailed analytics to help optimize your support strategy.
                  </div>
                  <div className="bg-indigo-100 text-indigo-800 p-4 rounded-lg self-start max-w-[80%]">
                    How long does it take to set up? ⏱️
                  </div>
                  <div className="bg-gray-100 p-4 rounded-lg self-end max-w-[80%]">
                    Most customers are up and running in less than 5 minutes!
                    Simply sign up, customize your widget, and add our code
                    snippet to your website. No technical expertise required.
                  </div>
                </div>
                <div className="mt-8 relative">
                  <input
                    type="text"
                    placeholder="Ask a question..."
                    className="w-full p-4 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  <Button
                    size="sm"
                    className="absolute right-2 top-2 bg-indigo-700 hover:bg-indigo-800"
                  >
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Demo;
