"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const Pricing = () => {
  return (
    <section id="pricing" className="py-28 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-20">
          <Badge className="mb-6 bg-indigo-100 text-indigo-800 hover:bg-indigo-200 border-none">
            Pricing
          </Badge>
          <h2 className="text-3xl sm:text-4xl font-bold mb-6">
            Simple, Transparent Pricing 💰
          </h2>
          <p className="text-gray-600 text-xl max-w-3xl mx-auto">
            Choose the plan that's right for your business. All plans include
            our core AI features.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-4xl mx-auto">
          <Card className="border-gray-200 hover:shadow-lg transition-shadow">
            <CardContent className="p-10">
              <h3 className="text-2xl font-bold mb-2">Free</h3>
              <div className="mb-8">
                <span className="text-4xl font-bold">$0</span>
                <span className="text-gray-500">/month</span>
              </div>
              <p className="text-gray-600 mb-8">
                Perfect for small websites and personal projects.
              </p>
              <ul className="space-y-4 mb-10">
                <li className="flex items-center">
                  <span className="text-indigo-500 mr-3 text-xl">✓</span>
                  <span className="text-gray-700">1 widget</span>
                </li>
                <li className="flex items-center">
                  <span className="text-indigo-500 mr-3 text-xl">✓</span>
                  <span className="text-gray-700">20 messages per month</span>
                </li>
                <li className="flex items-center">
                  <span className="text-indigo-500 mr-3 text-xl">✓</span>
                  <span className="text-gray-700">Basic analytics</span>
                </li>
                <li className="flex items-center">
                  <span className="text-indigo-500 mr-3 text-xl">✓</span>
                  <span className="text-gray-700">Community support</span>
                </li>
              </ul>
              <Link href="/signup" className="block w-full">
                <Button
                  variant="outline"
                  className="w-full border-indigo-600 text-indigo-700 hover:bg-indigo-50"
                >
                  Get Started
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="border-indigo-500 border-2 hover:shadow-lg transition-shadow relative">
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <Badge className="bg-indigo-700 hover:bg-indigo-800 text-white border-none px-3 py-1">
                Most Popular
              </Badge>
            </div>
            <CardContent className="p-10">
              <h3 className="text-2xl font-bold mb-2">Pro</h3>
              <div className="mb-8">
                <span className="text-4xl font-bold">$9</span>
                <span className="text-gray-500">/month</span>
              </div>
              <p className="text-gray-600 mb-8">
                Ideal for growing businesses and e-commerce sites.
              </p>
              <ul className="space-y-4 mb-10">
                <li className="flex items-center">
                  <span className="text-indigo-500 mr-3 text-xl">✓</span>
                  <span className="text-gray-700">10 widget</span>
                </li>
                <li className="flex items-center">
                  <span className="text-indigo-500 mr-3 text-xl">✓</span>
                  <span className="text-gray-700">1000 messages per month</span>
                </li>
                <li className="flex items-center">
                  <span className="text-indigo-500 mr-3 text-xl">✓</span>
                  <span className="text-gray-700">Advanced analytics</span>
                </li>
                <li className="flex items-center">
                  <span className="text-indigo-500 mr-3 text-xl">✓</span>
                  <span className="text-gray-700">Priority support</span>
                </li>
                <li className="flex items-center">
                  <span className="text-indigo-500 mr-3 text-xl">✓</span>
                  <span className="text-gray-700">Custom branding</span>
                </li>
                <li className="flex items-center">
                  <span className="text-indigo-500 mr-3 text-xl">✓</span>
                  <span className="text-gray-700">API access</span>
                </li>
              </ul>
              <Link href="/signup" className="block w-full">
                <Button className="w-full bg-indigo-700 hover:bg-indigo-800 text-white">
                  Get Started
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
