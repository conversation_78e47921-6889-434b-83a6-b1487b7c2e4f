"use client";

import { Message<PERSON><PERSON><PERSON>, Zap, <PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const Features = () => {
  return (
    <section id="features" className="py-28 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-20">
          <Badge className="mb-6 bg-indigo-100 text-indigo-800 hover:bg-indigo-200 border-none">
            Features
          </Badge>
          <h2 className="text-3xl sm:text-4xl font-bold mb-6">
            Everything You Need for Exceptional Support
          </h2>
          <p className="text-gray-600 text-xl max-w-3xl mx-auto">
            Our AI-powered FAQ widget provides instant, accurate answers to
            customer questions. 🤖
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
          <Card className="border-gray-200 hover:shadow-lg transition-shadow">
            <CardContent className="pt-8 pb-8">
              <div className="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                <MessageSquare className="w-7 h-7 text-indigo-700" />
              </div>
              <h3 className="text-xl font-semibold mb-3">
                AI-Powered Responses 🧠
              </h3>
              <p className="text-gray-600">
                Our advanced AI understands context and provides accurate,
                helpful responses to customer questions in real-time.
              </p>
            </CardContent>
          </Card>

          <Card className="border-gray-200 hover:shadow-lg transition-shadow">
            <CardContent className="pt-8 pb-8">
              <div className="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                <Zap className="w-7 h-7 text-indigo-700" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Lightning Fast ⚡</h3>
              <p className="text-gray-600">
                Get instant answers to customer questions, reducing wait times
                and improving satisfaction and conversion rates.
              </p>
            </CardContent>
          </Card>

          <Card className="border-gray-200 hover:shadow-lg transition-shadow">
            <CardContent className="pt-8 pb-8">
              <div className="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                <Shield className="w-7 h-7 text-indigo-700" />
              </div>
              <h3 className="text-xl font-semibold mb-3">
                Secure & Reliable 🔒
              </h3>
              <p className="text-gray-600">
                Enterprise-grade security and reliability to protect your data
                and ensure 99.9% uptime for your customers.
              </p>
            </CardContent>
          </Card>

          <Card className="border-gray-200 hover:shadow-lg transition-shadow">
            <CardContent className="pt-8 pb-8">
              <div className="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                <BarChart className="w-7 h-7 text-indigo-700" />
              </div>
              <h3 className="text-xl font-semibold mb-3">
                Detailed Analytics 📊
              </h3>
              <p className="text-gray-600">
                Track customer interactions, identify common questions, and
                optimize your support strategy with actionable insights.
              </p>
            </CardContent>
          </Card>

          <Card className="border-gray-200 hover:shadow-lg transition-shadow">
            <CardContent className="pt-8 pb-8">
              <div className="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="w-7 h-7 text-indigo-700"
                >
                  <path d="M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z"></path>
                  <path d="M7 7h.01"></path>
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-3">Custom Branding 🎨</h3>
              <p className="text-gray-600">
                Customize the widget's appearance to match your brand with
                custom colors, logos, and positioning options.
              </p>
            </CardContent>
          </Card>

          <Card className="border-gray-200 hover:shadow-lg transition-shadow">
            <CardContent className="pt-8 pb-8">
              <div className="w-14 h-14 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="w-7 h-7 text-indigo-700"
                >
                  <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                  <path d="m2 12 5.25 5 2.25-3H19l-4-6-4 6h-3.5l-2.25 3L2 12z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-3">
                Easy Integration 🔌
              </h3>
              <p className="text-gray-600">
                Add the widget to your website with a simple code snippet. No
                coding experience required.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default Features;
