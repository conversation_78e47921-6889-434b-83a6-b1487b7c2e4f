"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useUserSettings } from "@/store/useUserSettings";
import {
  BarChart3,
  MessageSquare,
  Calendar,
  CreditCard,
  AlertCircle,
  CheckCircle,
  Loader2,
  RefreshCw,
  Mail,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState("usage");
  const { data: session } = useSession();
  const {
    plan,
    usage,
    isLoading,
    error,
    fetchUserSettings,
    refreshUsage,
    canCreateWidget,
    canSendMessage,
    remainingMessages,
    remainingWidgets,
    usagePercentage,
  } = useUserSettings();

  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefreshUsage = async () => {
    setIsRefreshing(true);
    await refreshUsage();
    setIsRefreshing(false);
  };

  if (isLoading) {
    return (
      <div className="space-y-8">
        <header>
          <h1 className="text-3xl font-bold text-foreground">Settings</h1>
          <p className="mt-2 text-sm text-muted-foreground">
            Loading your plan and usage...
          </p>
        </header>
        <div className="flex items-center justify-center p-8">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-8">
        <header>
          <h1 className="text-3xl font-bold text-foreground">Settings</h1>
          <p className="mt-2 text-sm text-muted-foreground">
            Manage your plan and usage
          </p>
        </header>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Error loading settings: {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={fetchUserSettings}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <header>
        <h1 className="text-3xl font-bold text-foreground">Settings</h1>
        <p className="mt-2 text-sm text-muted-foreground">
          Manage your plan and usage
        </p>
      </header>

      {/* Tabs Navigation */}
      <div className="border-b border-border">
        <nav className="flex -mb-px space-x-8">
          <button
            onClick={() => setActiveTab("usage")}
            className={`py-4 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${
              activeTab === "usage"
                ? "border-primary text-primary"
                : "border-transparent text-muted-foreground hover:text-foreground hover:border-border"
            }`}
          >
            <BarChart3 className="w-4 h-4 mr-2" />
            Usage Stats
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === "usage" && (
          <>
            <div className="flex gap-4 w-full">
              {/* Subscription Status */}
              <Card className="p-6 bg-gradient-to-r from-blue-600 to-purple-600">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-white mb-2">
                      Current Plan
                    </h3>
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant={plan?.plan === "pro" ? "default" : "secondary"}
                        className="bg-white/20 text-white"
                      >
                        {plan?.plan === "pro" ? "Pro Plan" : "Free Plan"}
                      </Badge>
                      <Badge
                        variant={
                          plan?.status === "active" ? "default" : "destructive"
                        }
                        className="bg-white/20 text-white"
                      >
                        {plan?.status || "Active"}
                      </Badge>
                    </div>
                  </div>
                  <CreditCard className="w-6 h-6 text-white/80" />
                </div>

                <p className="text-sm text-white/80 mb-4">
                  {plan?.plan === "pro"
                    ? `You have access to ${usage?.limits.messages} AI responses and ${usage?.limits.widgets} widgets per billing period.`
                    : `You currently have access to ${usage?.limits.messages} AI responses and ${usage?.limits.widgets} widget per billing period with your free account.`}
                </p>

                {plan?.currentPeriodEnd && (
                  <p className="text-sm text-white/70 mb-4">
                    <Calendar className="w-4 h-4 inline mr-1" />
                    Plan renews on{" "}
                    {new Date(plan.currentPeriodEnd).toLocaleDateString()}
                  </p>
                )}

                <div className="flex space-x-2">
                  <Link href="/dashboard/credits">
                    <Button
                      variant="secondary"
                      size="sm"
                      className="bg-white text-primary hover:bg-white/90"
                    >
                      {plan?.plan === "pro"
                        ? "Manage Subscription"
                        : "Upgrade to Pro"}
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-white/20 text-white hover:bg-white/10"
                    onClick={handleRefreshUsage}
                    disabled={isRefreshing}
                  >
                    {isRefreshing ? (
                      <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                    ) : (
                      <RefreshCw className="w-4 h-4 mr-1" />
                    )}
                    Refresh
                  </Button>
                </div>
              </Card>
              {plan?.plan === "free" && (
                <Card className="p-6 border-2 border-primary/20">
                  <h3 className="text-lg font-medium mb-4">Upgrade to Pro</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <h4 className="font-medium text-sm mb-2">Free Plan</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        <li>• 20 AI responses/month</li>
                        <li>• 1 widget</li>
                        <li>• Basic support</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm mb-2">Pro Plan</h4>
                      <ul className="text-sm space-y-1">
                        <li className="text-green-600">
                          • 1,000 AI responses/month
                        </li>
                        <li className="text-green-600">• 10 widgets</li>
                        <li className="text-green-600">• Priority support</li>
                        <li className="text-green-600">• Advanced analytics</li>
                      </ul>
                    </div>
                  </div>
                  <Link href="/dashboard/credits">
                    <Button className="w-full">Upgrade to Pro</Button>
                  </Link>
                </Card>
              )}
            </div>

            {/* Usage Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Messages Usage */}
              <Card className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium flex items-center">
                    <MessageSquare className="w-5 h-5 mr-2 text-blue-500" />
                    AI Responses
                  </h3>
                  <Badge variant={canSendMessage ? "default" : "destructive"}>
                    {canSendMessage ? "Available" : "Limit Reached"}
                  </Badge>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">
                      Used this billing period
                    </span>
                    <span className="font-medium">
                      {usage?.messageCount || 0} / {usage?.limits.messages || 0}
                    </span>
                  </div>

                  <div className="text-xs text-muted-foreground">
                    Across all widgets
                  </div>

                  <Progress value={usagePercentage.messages} className="h-2" />

                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Remaining</span>
                    <span className="font-medium text-green-600">
                      {remainingMessages}
                    </span>
                  </div>
                </div>

                {!canSendMessage && (
                  <Alert className="mt-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      You've reached your message limit for this billing period.
                      Upgrade to continue or wait for your next billing cycle.
                    </AlertDescription>
                  </Alert>
                )}
              </Card>

              {/* Widgets Usage */}
              <Card className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium flex items-center">
                    <Mail className="w-5 h-5 mr-2 text-purple-500" />
                    Widgets
                  </h3>
                  <Badge variant={canCreateWidget ? "default" : "destructive"}>
                    {canCreateWidget ? "Available" : "Limit Reached"}
                  </Badge>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Created</span>
                    <span className="font-medium">
                      {usage?.widgetCount || 0} / {usage?.limits.widgets || 0}
                    </span>
                  </div>

                  <Progress value={usagePercentage.widgets} className="h-2" />

                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Remaining</span>
                    <span className="font-medium text-green-600">
                      {remainingWidgets}
                    </span>
                  </div>
                </div>

                {!canCreateWidget && (
                  <Alert className="mt-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      You've reached your widget limit. Upgrade to create more.
                    </AlertDescription>
                  </Alert>
                )}
              </Card>
            </div>

            {/* Usage Reset Info */}
            {usage?.resetDate && (
              <Card className="p-4">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>
                    Usage limits reset on{" "}
                    {new Date(usage.resetDate).toLocaleDateString()} (based on
                    your signup date)
                  </span>
                </div>
              </Card>
            )}

            {/* Plan Comparison */}
          </>
        )}
      </div>
    </div>
  );
}
