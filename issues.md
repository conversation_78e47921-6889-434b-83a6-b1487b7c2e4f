- [ ] When the widget is made inactive from configuration, it shouldnt show up in the product page. even if the script is there.
- [ ] in the /dashboard/widgets/[id]/edit preview, the widget isnt opening when clicked. reuse the preview component from /dashboard/widgets/new page to properly show a interactable preview.
- [ ] fix: feedback count not updating on widgets analytics card (/dashboard/widgets/[id]). Even when theres multiple feedbacks on that widget. (it is showing on the dashboard feedbacks count tho)
- [ ] fix: AI Responses usage still not showing the (/dashboard/settings) page. Although there's 4 messages sent (it does show in the dashboard and the widgets analytics)